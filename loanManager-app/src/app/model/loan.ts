import {LoanP<PERSON>} from "./loanPlan";
import {<PERSON><PERSON><PERSON>} from "./borrower";
import {MetaData} from "./metaData";

export class Loan {

  id: string | undefined;
  loanNo: string = "";
  dateTime: string | undefined;
  loanPlan: LoanPlan | undefined;
  borrower: Borrower = new Borrower;
  loanAmount: number | undefined;
  loanAmountWithInterest: number | undefined;
  overPaidAmount: number | undefined;
  arrearsAmount: number | undefined;
  arrearsRecordCount: number | undefined;
  installmentAmount: number | undefined;
  installmentLeft: number | undefined;
  balance: number | undefined;
  status: MetaData | undefined;
  statusCode: string | undefined;
  nextPaymentRecordDate: Date | undefined;
  deductFromLedger: boolean = true;
  appNo: string | undefined;
  approvedDate: Date | undefined;
  settlementDate: Date | undefined;
  rejectedDate: Date | undefined;
  rejectedReason: string | undefined;
  approvedBy: string | undefined;
  createdDate: Date | undefined;
  createdBy: string | undefined;
  lastModifiedDate: Date | undefined;
  lastModifiedBy: string | undefined;

}
