import {Component, OnInit} from '@angular/core';
import {LoanService} from "../service/loan.service";
import {Loan} from "../model/loan";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>er, ModalController, ViewWillEnter} from "@ionic/angular";
import {NewBorrowerComponent} from "../new-borrower/new-borrower.component";
import {Borrower} from "../model/borrower";
import {LoanPlan} from "../model/loanPlan";
import {LoanPlanService} from "../service/loanPlanService";
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-history',
  templateUrl: './new-loan.component.html',
  styleUrls: ['./new-loan.component.scss']
})
export class NewLoanComponent implements OnInit, ViewWillEnter {

  loan: Loan = new Loan();
  loanPlans: Array<LoanPlan> = [];
  dateSelected: string;
  submitDisabled = false;

  loanInstallment: number = 0;
  repaymentAmount: number = 0;

  constructor(private loanService: LoanService, private modalController: ModalController,
              private loanPlanService: LoanPlanService, private alertCtrl: AlertController) {
    this.dateSelected = new Date().toISOString();
  }

  ngOnInit() {

  }

  ionViewWillEnter() {
    this.loan.borrower = new Borrower();
    this.loadPlans();
    this.submitDisabled = false;
  }

  async openBorrowers() {
    const modal = await this.modalController.create({
      component: NewBorrowerComponent
    });
    modal.onDidDismiss().then((data) => {
      if (data.data) {
        this.loan.borrower = data.data;
      }
    });
    return await modal.present();
  }

  loadPlans() {
    this.loanPlanService.findAllLoanPlan().subscribe((data: Array<LoanPlan>) => {
      return this.loanPlans = data;
    });
  }

  save() {
    this.submitDisabled = true;
    this.loan.dateTime = this.dateSelected;
    this.loan.appNo = environment.appNo;

    // Set default status code to Pending for mobile app loans
    this.loan.statusCode = '1'; // Pending status

    this.loanService.save(this.loan).subscribe((data: any) => {
      if (data.code === 200) {
        this.showAlert(data.message);
        this.clear();
      } else {
        this.showAlert(data.message);
        this.submitDisabled = false;
      }
    });
  }

  async showAlert(message: string) {
    const alert = await this.alertCtrl.create({
      header: 'දැන්වීම',
      message: message,
      buttons: ['වසන්න']
    });
    await alert.present();
  }

  calculateAmounts() {
    if (this.loan.loanAmount != undefined && this.loan.loanPlan != undefined) {
      let loanDurationInDays = this.loan.loanPlan.durationInDays;
      this.repaymentAmount = this.loan.loanAmount +
        (this.loan.loanAmount * this.loan.loanPlan.interestRate / 100 / 30 * loanDurationInDays);
      this.loanInstallment = this.repaymentAmount / this.loan.loanPlan.totalNoOfInstallments;
    }
  }

  clear() {
    this.loan = new Loan();
    this.submitDisabled = false;
    this.loanInstallment = 0;
    this.repaymentAmount = 0;
  }

}
