package lk.sout.loanManager.business.controller;

import lk.sout.loanManager.business.entity.Loan;
import lk.sout.loanManager.business.service.LoanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON> We<PERSON> on 12/15/2019
 */
@RestController
@RequestMapping("/loan")
public class LoanController {

    @Autowired
    private LoanService loanService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Loan loan) {
        try {
            return ResponseEntity.ok(loanService.create(loan, "Web"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> update(@RequestBody Loan loan) {
        try {
            Loan updatedLoan = loanService.save(loan);
            if (updatedLoan != null) {
                return ResponseEntity.ok().body(Map.of(
                    "code", 200,
                    "message", "Loan updated successfully",
                    "data", updatedLoan
                ));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                    "code", 500,
                    "message", "Failed to update loan"
                ));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).body(Map.of(
                "code", 409,
                "message", "Error updating loan: " + e.getMessage()
            ));
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAllLoans(@RequestParam("page") String page, @RequestParam("pageSize")
    String pageSize) {
        return ResponseEntity.ok(loanService.findAll(PageRequest.of(Integer.parseInt(page),
                Integer.parseInt(pageSize))));
    }

    @RequestMapping(value = "/findAllPending", method = RequestMethod.GET)
    public ResponseEntity<?> findAllPending(@RequestParam("page") String page, @RequestParam("pageSize")
    String pageSize) {
        return ResponseEntity.ok(loanService.findAllPending(PageRequest.of(Integer.parseInt(page),
                Integer.parseInt(pageSize))));
    }

    @RequestMapping(value = "/findAllArrears", method = RequestMethod.GET)
    public ResponseEntity<?> findAllArrears(@RequestParam("page") String page, @RequestParam("pageSize")
    String pageSize) {
        return ResponseEntity.ok(loanService.findAllArrears(PageRequest.of(Integer.parseInt(page),
                Integer.parseInt(pageSize))));
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findById(@RequestParam String id) {
        try {
            return ResponseEntity.ok(loanService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/approveLoan", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> approveLoan(@RequestParam String loanNo) {
        try {
            return ResponseEntity.ok(loanService.approve(loanNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/rejectLoan", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> rejectLoan(@RequestParam String loanNo, @RequestParam String reason) {
        try {
            return ResponseEntity.ok(loanService.reject(loanNo, reason));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByBorrowerNic", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByBorrowerNic(@RequestParam String nic) {
        try {
            return ResponseEntity.ok(loanService.findByBorrowerNic(nic));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByLoanNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByLoanNo(@RequestParam String loanNo) {
        try {
            return ResponseEntity.ok(loanService.findByLoanNo(loanNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findLoanByStatus", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findLoanByStatus(@RequestParam String statusId,@RequestParam("page") String page,
                                               @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(loanService.findAllByStatus(statusId,PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
