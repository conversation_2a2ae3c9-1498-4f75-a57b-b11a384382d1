package lk.sout.loanManager.business.service;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.service.MetaDataService;
import lk.sout.loanManager.business.entity.Loan;
import lk.sout.loanManager.business.entity.LoanRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class LoanSchedules {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoanSchedules.class);

    @Autowired
    LoanRecordService loanRecordService;

    @Autowired
    Response response;

    @Autowired
    LoanService loanService;

    @Autowired
    MetaDataService metaDataService;

    @Scheduled(cron = "${loan.cron.generateLoanRecord}", zone = "${loan.cron.timezone}")
    public void generateLoanRecord() {
        try {
            MetaData loanStatus = metaDataService.searchMetaData("Settled", "Loan Status");

            List<Loan> loans = loanService.findAllByStatusNot(loanStatus);
            for (Loan loan : loans) {
                int loanRecCount = loanRecordService.countByLoanNo(loan.getLoanNo());
                if (loanRecCount < loan.getLoanPlan().getTotalNoOfInstallments()) {
                    if (null != loan.getNextPaymentRecordDate() && !loan.getNextPaymentRecordDate().isAfter(LocalDate.now())) {
                        loanRecordService.create(loan.getLoanNo(), "Auto", 0.0);
                    }
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Scheduler Error : " + ex.getMessage());
        }
    }

    @Scheduled(cron = "${loan.cron.createArrearsRecords}", zone = "${loan.cron.timezone}")
    @Transactional
    public void createArrearsRecords() {
        try {
            List<LoanRecord> loanRecords = loanRecordService.findUnPaidTodayList();
            MetaData arrears = metaDataService.searchMetaData("Arrears", "Loan Record Status");
            for (LoanRecord loanRecord : loanRecords) {
                loanRecord.setStatus(arrears);
                loanRecord.setStatusCode(arrears.getName());
                loanRecord.setDaysDue(1);
                loanRecordService.save(loanRecord);
            }
        } catch (Exception ex) {
            LOGGER.error("Booking backup failed : " + ex.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }

    @Scheduled(cron = "${loan.cron.setLoanAsArrears}", zone = "${loan.cron.timezone}")
    @Transactional
    public void setLoanAsArrears() {
        try {
            MetaData arrears = metaDataService.searchMetaData("Arrears", "Loan Status");
            MetaData current = metaDataService.searchMetaData("Current", "Loan Status");

            MetaData loanRecordArrears = metaDataService.searchMetaData("Arrears", "Loan Record Status");

            List<Loan> loans = loanService.findAllByStatus(current.getId());

            for (Loan loan : loans) {
                if (loanRecordService.countByStatusAndLoanNo(loanRecordArrears, loan.getLoanNo()) >
                        loan.getLoanPlan().getArrearsInterestDuration()) {
                    loan.setStatus(arrears);
                    loan.setStatusCode(arrears.getName());
                    loanService.save(loan);
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Booking backup failed : " + ex.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }

    @Scheduled(cron = "${loan.cron.updateArrearsRecords}", zone = "${loan.cron.timezone}")
    @Transactional
    public void manageArrearsLoans() {
        try {
            MetaData arrearsLoanRecMeta = metaDataService.searchMetaData("Arrears", "Loan Record Status");
            MetaData arrearsLoanMeta = metaDataService.searchMetaData("Arrears", "Loan Status");
            List<Loan> loans = loanService.findAllByStatus(arrearsLoanMeta.getId());

            for (Loan loan : loans) {

                List<LoanRecord> loanRecords = loanRecordService.findByStatusAndLoanNo(arrearsLoanRecMeta, loan.getLoanNo());

                for (LoanRecord loanRecord : loanRecords) {

                    if (loanRecords.size() > loan.getLoanPlan().getArrearsInterestDuration()) {

                        loanRecord.setDaysDue(loanRecord.getDaysDue() + 1);

                        double arrearsRate = loan.getLoanPlan().getArrearsInterestRate();
                        double recordBalance = loan.getInstallmentAmount() - loanRecord.getPaidAmount();
                        double arrearsAmount = recordBalance * (arrearsRate / 100);

                        loanRecord.setArrearsAmount(loanRecord.getArrearsAmount() + arrearsAmount);
                        loanRecord.setInstallmentAmount(loanRecord.getInstallmentAmount() + arrearsAmount);
                        loanRecord.setBalance(loanRecord.getInstallmentAmount() - loanRecord.getPaidAmount());
                        loanRecordService.save(loanRecord);

                        loan.setArrearsAmount(loan.getArrearsAmount() + arrearsAmount);
                        loan.setBalance(loan.getBalance() + arrearsAmount);
                        loan.setLoanAmountWithInterest(loan.getLoanAmountWithInterest() + arrearsAmount);
                        loanService.save(loan);

                    } else {
                        loanRecord.setDaysDue(loanRecord.getDaysDue() + 1);
                        loanRecordService.save(loanRecord);
                    }
                }
            }
        } catch (
                Exception ex) {
            LOGGER.error("updateArrearsRecords failed : " + ex.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }

 /*   @Scheduled(cron = "${loan.cron.updateArrearsRecords}", zone = "${loan.cron.timezone}")
    @Transactional
    public void updateArrearsRecords() {
        try {
            MetaData arrears = metaDataService.searchMetaData("Arrears", "Loan Record Status");
            List<LoanRecord> loanRecords = loanRecordService.findByStatus(arrears);

            for (LoanRecord loanRecord : loanRecords) {
                Loan loan = loanService.findByLoanNo(loanRecord.getLoanNo());
                if (loanRecord.getDaysDue() > loan.getLoanPlan().getArrearsInterestDuration()) {
                    loanRecord.setDaysDue(loanRecord.getDaysDue() + 1);

                    double arrearsRate = loan.getLoanPlan().getArrearsInterestRate();
                    double installmentInterest = loan.getInstallmentAmount() -
                            (loan.getLoanAmount() / loan.getLoanPlan().getTotalNoOfInstallments());
                    double arrearsAmount = installmentInterest * (arrearsRate / 30);

                    loanRecord.setArrearsAmount(loanRecord.getArrearsAmount() + arrearsAmount);
                    loanRecordService.save(loanRecord);

                    loan.setLoanAmountWithInterest(loan.getLoanSettlementAmount() + arrearsAmount);
                    loanService.save(loan);
                    System.out.println(loan.getLoanNo() + " : " + arrearsAmount + " : " + loan.getLoanAmountWithInterest());
                } else {
                    loanRecord.setDaysDue(loanRecord.getDaysDue() + 1);
                    loanRecordService.save(loanRecord);
                }
            }
        } catch (Exception ex) {
            LOGGER.error("updateArrearsRecords failed : " + ex.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }*/

    @Scheduled(cron = "${loan.cron.manageOverPaidAmnt}", zone = "${loan.cron.timezone}")
    @Transactional
    public void autoSettleByOverPayments() {
        try {
            MetaData settledStatus = metaDataService.searchMetaData("Settled", "Loan Status");
            List<Loan> loans = loanService.findAllByStatusNot(settledStatus);

            for (Loan loan : loans) {
                double overPayment = loan.getOverPaidAmount();
                if (overPayment > 0) {
                    MetaData arrears = metaDataService.searchMetaData("Arrears", "Loan Record Status");
                    MetaData settled = metaDataService.searchMetaData("Settled", "Loan Record Status");
                    List<LoanRecord> loanRecords = loanRecordService.findByStatusAndLoanNoOrderByBalance(arrears, loan.getLoanNo());
                    for (LoanRecord loanRecord : loanRecords) {
                        double recBalance = loan.getInstallmentAmount() - loanRecord.getPaidAmount();

                        if (overPayment >= recBalance) {
                            loanRecord.setPaidAmountByOverPaid(recBalance);
                            loanRecord.setPaidDate(LocalDate.now());
                            loanRecord.setStatusCode("3"); // Set to "3" for Settled loan record status
                            loanRecord.setStatus(settled);
                            overPayment = overPayment - recBalance;
                            loanRecordService.save(loanRecord);
                        }
                    }
                    loan.setOverPaidAmount(overPayment);
                    loanService.save(loan);
                }
            }
        } catch (Exception ex) {
            LOGGER.error("autoSettleByOverPayments failed : " + ex.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }

    @Scheduled(cron = "${loan.cron.moveOldRecords}", zone = "${loan.cron.timezone}")
    @Transactional
    public void moveOldRecords() {
        try {


        } catch (Exception ex) {
            LOGGER.error("Booking backup failed : " + ex.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }


}
