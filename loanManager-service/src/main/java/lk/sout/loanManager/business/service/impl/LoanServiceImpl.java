package lk.sout.loanManager.business.service.impl;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.Sequence;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.SequenceService;
import lk.sout.core.service.UserService;
import lk.sout.loanManager.business.entity.Loan;
import lk.sout.loanManager.business.entity.LoanRecord;
import lk.sout.loanManager.business.entity.RejectedLoan;
import lk.sout.loanManager.business.repository.LoanRepository;
import lk.sout.loanManager.business.service.LedgerRecordService;
import lk.sout.loanManager.business.service.LoanRecordService;
import lk.sout.loanManager.business.service.LoanService;
import lk.sout.loanManager.business.service.RejectedLoanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class LoanServiceImpl implements LoanService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoanServiceImpl.class);

    @Autowired
    LoanRepository loanRepository;

    @Autowired
    LoanRecordService loanRecordService;

    @Autowired
    SequenceService sequenceService;

    @Autowired
    Sequence sequence;

    @Autowired
    Response response;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    UserService userService;

    @Autowired
    LedgerRecordService ledgerRecordService;

    @Autowired
    RejectedLoanService rejectedLoanService;

    @Override
    @Transactional
    public Response create(Loan loan) {
        try {
            MetaData pendingStatus = metaDataService.searchMetaData("Pending", "Loan Status");
            MetaData currentStatus = metaDataService.searchMetaData("Current", "Loan Status");
            MetaData settledStatus = metaDataService.searchMetaData("Settled", "Loan Status");

            if (loan.getStatus() == null) {
                loan.setStatus(pendingStatus);
                loan.setStatusCode("1"); // Set to "1" for Pending status
            }

            String seqId = "";
            sequence = sequenceService.findSequenceByName("Loan");
            seqId = (sequence.getPrefix() + String.valueOf((sequence.getCounter() + 1)));
            loan.setLoanNo(seqId);
            sequence = null;

            loan.setBorrowerNic(loan.getBorrower().getNic().trim().toUpperCase());
            int loanDurationInDays = loan.getLoanPlan().getDurationInDays();
            double loanAmountWithInterest = loan.getLoanAmount() + (loan.getLoanAmount() * loan.getLoanPlan().getInterestRate() / 100 / 30 * loanDurationInDays);
            double installmentAmount = loanAmountWithInterest / (loanDurationInDays / loan.getLoanPlan().getPaymentFrequencyInDays());
            loan.setInstallmentAmount(Math.round(installmentAmount));
            loan.setInstallmentWithoutInterest(loan.getLoanAmount() / loan.getLoanPlan().getTotalNoOfInstallments());
            loan.setLoanAmountWithInterest(loanAmountWithInterest);
            loan.setLoanSettlementAmount(loanAmountWithInterest);

            //Set loan balance if loan status is pending
            if (loan.getStatus().getId().equals(pendingStatus.getId())) {
                loan.setBalance(loanAmountWithInterest);
                loan.setPaidAmount(0.0);
            }

            if (loan.getStatus().getId().equals(settledStatus.getId())) {
                loan.setBalance(0);
                loan.setPaidAmount(loanAmountWithInterest);
            }

            if (loan.getStatus().getId().equals(currentStatus.getId())) {
                loan.setActive(true);
                loan.setNextPaymentRecordDate(LocalDate.now().plusDays(loan.getLoanPlan().getPaymentFrequencyInDays()));
            }

            loan = loanRepository.save(loan);
            sequenceService.incrementSequence("Loan");

            //approving same time that creating a pending loan
            /*if (loan.getStatus().getId().equals(pendingStatus.getId())) {
                approve(loan.getLoanNo());
            }*/

            if (loan.isDeductFromLedger()) {
                ledgerRecordService.create(loan.getAppNo(), loan.getLoanAmount(), "New Loan", loan.getLoanNo(),
                        loan.getBorrower().getName(), "-");
            }

            response.setCode(200);
            response.setMessage("Loan Created Successfully");
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Creating Loan Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Loan Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }

    @Override
    public Loan save(Loan loan) {
        try {
            return loanRepository.save(loan);
        } catch (Exception ex) {
            LOGGER.error("Saving Loan Failed " + ex.getMessage());
        }
        return null;
    }

    @Override
    public boolean approve(String loanNo) {
        try {
            MetaData loanStatus = metaDataService.searchMetaData("Issued", "Loan Status");

            Loan loan = loanRepository.findAllByLoanNo(loanNo);
            loan.setStatus(loanStatus);
            loan.setStatusCode("3"); // Set to "3" for Current status
            loan.setActive(true);
            loan.setApprovedBy(userService.getCurrentUser() != null ? userService.findLoggedInUser().getUsername() : "Mobile App");
            loan.setApprovedDate(LocalDate.now());
            loan.setNextPaymentRecordDate(LocalDate.now().plusDays(loan.getLoanPlan().getPaymentFrequencyInDays()));
            loan.setInstallmentLeft(loan.getLoanPlan().getTotalNoOfInstallments());
            int duration = loan.getLoanPlan().getDurationInDays();
            loan.setSettlementDate(LocalDate.now().plusDays(duration + (duration / 30)));
            loanRepository.save(loan);

            return true;
        } catch (Exception ex) {
            LOGGER.error("Approving Loan Failed " + ex.getMessage());
        }
        return false;
    }

    @Override
    public boolean settleLoan(String loanNo) {
        try {
            Loan loan = loanRepository.findAllByLoanNo(loanNo);

            MetaData settledLoan = metaDataService.searchMetaData("Settled", "Loan Status");
            loan.setStatus(settledLoan);
            loan.setStatusCode("5"); // Explicitly set to "5" for settled loans
            loan.setActive(false); // Set active to false for settled loans
            loan.setArrearsAmount(0.0);
            loan.setBalance(0.0);
            loan.setLoanSettlementAmount(loan.getPaidAmount());
            loan.setLoanAmountWithInterest(loan.getPaidAmount());
            loan.setArrearsRecordCount(0);
            loanRepository.save(loan);

            List<LoanRecord> loanRecords = loanRecordService.findAllRecordsByLoanNo(loanNo);
            MetaData settledLoanRec = metaDataService.searchMetaData("Settled", "Loan Record Status");

            for (LoanRecord record : loanRecords) {
                record.setStatus(settledLoanRec);
                record.setStatusCode("3"); // Explicitly set to "3" for settled loan records
                loanRecordService.save(record);
            }

            LOGGER.info("Loan " + loanNo + " has been settled successfully");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Settling Loan Failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean reject(String loanNo, String reason) {
        try {
            // Find the loan to be rejected
            Loan loan = loanRepository.findAllByLoanNo(loanNo);
            if (loan == null) {
                LOGGER.error("Loan not found for rejection: " + loanNo);
                return false;
            }

            // Create a RejectedLoan record
            RejectedLoan rejectedLoan = new RejectedLoan(
                loan.getLoanNo(),
                loan.getBorrower().getName(),
                loan.getBorrower().getNic(),
                loan.getBorrower().getTelephone1(),
                loan.getLoanAmount(),
                loan.getLoanPlan().getName(),
                reason,
                userService.getCurrentUser() != null ? userService.findLoggedInUser().getUsername() : "System"
            );

            // Save the rejected loan record
            rejectedLoanService.save(rejectedLoan);

            // Delete the original loan from the loans collection
            loanRepository.delete(loan);

            LOGGER.info("Loan " + loanNo + " has been rejected and moved to rejected loans collection");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Rejecting Loan Failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Page<Loan> findAll(Pageable pageable) {
        try {
            return loanRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Page<Loan> findAllPending(Pageable pageable) {
        try {
            MetaData loanStatus = metaDataService.searchMetaData("Pending", "Loan Status");
            return loanRepository.findAllByStatus(loanStatus.getId(), pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Page<Loan> findAllArrears(Pageable pageable) {
        try {
            MetaData loanStatus = metaDataService.searchMetaData("Arrears", "Loan Status");
            return loanRepository.findAllByStatus(loanStatus.getId(), pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Loan> findAllByStatus(String status) {
        try {
            return loanRepository.findAllByStatus(status);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Loan> findAllByStatusNot(MetaData status) {
        try {
            return loanRepository.findAllByStatusNot(status);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Loan> findAllByStatusNotIn(List<MetaData> status) {
        try {
            return loanRepository.findAllByStatusNotIn(status);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Loan> findAllByStatusIn(List<MetaData> status) {
        try {
            return loanRepository.findAllByStatusIn(status);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Loan> findAllByStatus(String status, Pageable pageable) {
        try {
            return loanRepository.findAllByStatus(status, pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Loans by status Failed " + ex.getMessage());
            return null;
        }
    }


    @Override
    public Loan findById(String id) {
        try {
            return loanRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Find Loan by Id Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Loan findByLoanNo(String loanNo) {
        try {
            return loanRepository.findAllByLoanNo(loanNo);
        } catch (Exception ex) {
            LOGGER.error("Find Loan by Id Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Loan> findByBorrowerNic(String nic) {
        try {
            return loanRepository.findAllByBorrowerNic(nic);
        } catch (Exception ex) {
            LOGGER.error("Find Loan by Id Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Loan> findActiveLoanByNic(String nic) {
        try {
            MetaData loanStatus = metaDataService.searchMetaData("Settled", "Loan Status");
            return loanRepository.findByBorrowerNicAndStatusNot(nic, loanStatus);
        } catch (Exception ex) {
            LOGGER.error("Find Loan by Id Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Loan> findByBorrowerId(String id) {
        try {
            return loanRepository.findAllByBorrower(id);
        } catch (Exception ex) {
            LOGGER.error("Find Loan by Id Failed " + ex.getMessage());
            return null;
        }
    }



}
