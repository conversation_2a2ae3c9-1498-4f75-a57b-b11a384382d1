package lk.sout.loanManager.mobileApp.repository;

import lk.sout.core.entity.MetaData;
import lk.sout.core.service.MetaDataService;
import lk.sout.loanManager.business.entity.Loan;
import lk.sout.loanManager.business.entity.MonthlyStats;
import lk.sout.loanManager.mobileApp.model.LoanCountStats;
import lk.sout.loanManager.mobileApp.model.LoanStat;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;

/**
 * Created by <PERSON><PERSON><PERSON> on 5/27/2023
 */

@Repository
public class StatRepository {

    private final MongoTemplate mongoTemplate;

    @Autowired
    private MetaDataService metaDataService;

    public StatRepository(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public LoanCountStats loanCountStat() {
        try {
            int activeLoan = 0;
            int arrearsLoan = 0;
            int settledLoanCount = 0;
            int totalLoan = 0;

            LoanCountStats stats = new LoanCountStats();

            Query queryActive = new Query();
            queryActive.addCriteria(Criteria.where("statusCode").in("2", "3")); // Only Issued and Current
            activeLoan = (int) mongoTemplate.count(queryActive, Loan.class);

            Query queryArrears = new Query();
            queryArrears.addCriteria(Criteria.where("statusCode").is("4"));
            arrearsLoan = (int) mongoTemplate.count(queryArrears, Loan.class);

            Query queryCurrent = new Query();
            queryCurrent.addCriteria(Criteria.where("statusCode").is("5"));
            settledLoanCount = (int) mongoTemplate.count(queryCurrent, Loan.class);

            // If no settled loans found by statusCode, try alternative approaches
            if (settledLoanCount == 0) {
                try {
                    // Try to get the settled status metadata first
                    MetaData settledStatus = metaDataService.searchMetaData("Settled", "Loan Status");
                    if (settledStatus != null) {
                        Query querySettledByStatus = new Query();
                        querySettledByStatus.addCriteria(Criteria.where("status").is(settledStatus));
                        settledLoanCount = (int) mongoTemplate.count(querySettledByStatus, Loan.class);
                    }
                } catch (Exception ex) {
                    // If that fails, try checking for inactive loans as a fallback
                    Query queryInactive = new Query();
                    queryInactive.addCriteria(Criteria.where("active").is(false));
                    settledLoanCount = (int) mongoTemplate.count(queryInactive, Loan.class);
                }
            }

            Query queryTotal = new Query();
            totalLoan = (int) mongoTemplate.count(queryTotal, Loan.class);

            stats.setActiveLoanCount(activeLoan);
            stats.setArrearsLoanCount(arrearsLoan);
            stats.setSettledLoanCount(settledLoanCount);
            stats.setTotalLoanCount(totalLoan);

            // Debug logging
            System.out.println("Loan Count Stats - Total: " + totalLoan + ", Active: " + activeLoan +
                             ", Arrears: " + arrearsLoan + ", Settled: " + settledLoanCount);

            return stats;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public MonthlyStats monthlyStat() {
        try {

            double monthlyProfit = 0.0;
            double monthlyCollection = 0.0;

            LocalDateTime startDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
            LocalDateTime endDate = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()).plusDays(1).atStartOfDay();

            MonthlyStats stats = new MonthlyStats();

            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(Criteria.where("dateTime").gte(startDate).lte(endDate)),
                    Aggregation.group()
                            .sum("amount").as("monthlyCollectionAmount")
                            .sum("profit").as("monthlyProfit")
            );

            Document resultDocument = mongoTemplate.aggregate(aggregation, "payment", Document.class)
                    .getUniqueMappedResult();

            monthlyCollection = resultDocument != null ? resultDocument.getDouble("monthlyCollectionAmount") : 0.0;
            monthlyProfit = resultDocument != null ? resultDocument.getDouble("monthlyProfit") : 0.0;

            stats.setMonthlyProfit(monthlyProfit);
            stats.setMonthlyCollection(monthlyCollection);
            return stats;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public LoanStat settledLoanStat() {
        try {
            double totalLoanAmount = 0.0;
            double totalPaidAmount = 0.0;

            LoanStat stats = new LoanStat();

            Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(Criteria.where("statusCode").is("5")),
                    Aggregation.group().sum("paidAmount").as("totalPaidAmount")
                            .sum("loanAmount").as("totalLoanAmount")
            );

            Document resultDocument = mongoTemplate.aggregate(aggregation, "loan", Document.class)
                    .getUniqueMappedResult();

            totalPaidAmount = resultDocument != null ? resultDocument.getDouble("totalPaidAmount") : 0.0;
            totalLoanAmount = resultDocument != null ? resultDocument.getDouble("totalLoanAmount") : 0.0;

            stats.setAmountLent(totalLoanAmount);
            stats.setAmountCollected(totalPaidAmount);
            stats.setProfit(totalPaidAmount - totalLoanAmount);

            return stats;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }

    }

    public LoanStat ongoingLoanStat() {
        try {

            double totalLoanAmount = 0.0;
            double totalPaidAmount = 0.0;

            LoanStat stats = new LoanStat();

            Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(Criteria.where("statusCode").nin("1", "5")),
                    Aggregation.group().sum("paidAmount").as("totalPaidAmount")
                            .sum("loanAmountWithInterest").as("totalLoanAmount")
            );

            Document resultDocument = mongoTemplate.aggregate(aggregation, "loan", Document.class)
                    .getUniqueMappedResult();

            totalPaidAmount = resultDocument != null ? resultDocument.getDouble("totalPaidAmount") : 0.0;
            totalLoanAmount = resultDocument != null ? resultDocument.getDouble("totalLoanAmount") : 0.0;

            stats.setAmountLent(totalLoanAmount);
            stats.setAmountCollected(totalPaidAmount);
            stats.setProfit(totalPaidAmount - totalLoanAmount);

            return stats;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public LoanStat allLoanStat() {
        try {

            double totalSettledAmount = 0.0;
            double totalPaidAmount = 0.0;
            double totalLentAmount = 0.0;
            double totalCollectible = 0.0;

            LoanStat stats = new LoanStat();

            Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(Criteria.where("statusCode").ne("1")),
                    Aggregation.group().sum("loanAmountWithInterest").as("totalAmountWithInterest")
                            .sum("paidAmount").as("totalPaidAmount")
                            .sum("loanAmount").as("totalLentAmount")
            );

            Document resultDocument = mongoTemplate.aggregate(aggregation, "loan", Document.class)
                    .getUniqueMappedResult();

            totalPaidAmount = resultDocument != null ? resultDocument.getDouble("totalPaidAmount") : 0.0;
            totalCollectible = resultDocument != null ? resultDocument.getDouble("totalAmountWithInterest") : 0.0;
            totalLentAmount = resultDocument != null ? resultDocument.getDouble("totalLentAmount") : 0.0;

            stats.setAmountLent(totalLentAmount);
            stats.setAmountCollected(totalPaidAmount);
            stats.setCollectibleAmount(totalCollectible);
            stats.setProfit(totalCollectible - totalLentAmount);
            return stats;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

}
