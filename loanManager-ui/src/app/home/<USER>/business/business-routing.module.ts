import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CreateLoanPlanComponent} from "./component/create-loan-plan/create-loan-plan.component";
import {ManageLoanPlanComponent} from "./component/manage-loan-plan/manage-loan-plan.component";
import {CreateLoanComponent} from "./component/create-loan/create-loan.component";
import {AllLoanComponent} from "./component/manage-loan/all-loans/all-loan.component";
import {LoanPaymentComponent} from "./component/loan-payment/loan-payment.component";
import {RouteComponent} from "./component/route/route.component";
import {LoanDetailsComponent} from "./component/loan-details/loan-details.component";
import {PendingLoanComponent} from "./component/manage-loan/pending-loans/pending-loan.component";
import {ArrearsLoanComponent} from "./component/manage-loan/arrears-loans/arrears-loan.component";
import {LedgerComponent} from "./component/ledger/ledger.component";
import {HolidayManagementComponent} from "./component/holiday-management/holiday-management.component";
import {EditLoanComponent} from "./component/manage-loan/edit-loan/edit-loan.component";
import {RejectedLoansComponent} from "./component/manage-loan/rejected-loans/rejected-loans.component";

const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full'
  },
  {
    path: 'create_loan_plan',
    component: CreateLoanPlanComponent
  },
  {
    path: 'manage_loan_plan',
    component: ManageLoanPlanComponent
  },
  {
    path: 'manage_routes',
    component: RouteComponent
  },
  {
    path: 'create_loan',
    component: CreateLoanComponent
  },
  {
    path: 'pending_loan',
    component: PendingLoanComponent
  },
  {
    path: 'arrears_loan',
    component: ArrearsLoanComponent
  },
  {
    path: 'all_loan',
    component: AllLoanComponent
  },
  {
    path: 'loan_payment',
    component: LoanPaymentComponent
  },
  {
    path: 'ledger',
    component: LedgerComponent
  },
  {
    path: 'holiday_management',
    component: HolidayManagementComponent
  },
  {
    path: 'edit_loan',
    component: EditLoanComponent
  },
  {
    path: 'rejected_loans',
    component: RejectedLoansComponent
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class BusinessRoutingModule {
}

export const businessRouteParams = [CreateLoanPlanComponent, ManageLoanPlanComponent, CreateLoanComponent,
  PendingLoanComponent, ArrearsLoanComponent, AllLoanComponent, LoanPaymentComponent, RouteComponent,
  LoanDetailsComponent, LedgerComponent, HolidayManagementComponent, EditLoanComponent, RejectedLoansComponent];
