import {Component, OnInit} from '@angular/core';
import {Loan} from "../../model/loan";
import {NgForm} from "@angular/forms";
import {<PERSON>rrower} from "../../../borrower/model/borrower";
import {BorrowerService} from "../../../borrower/service/borrower.service";
import {LoanPlan} from "../../model/loanPlan";
import {LoanPlanService} from "../../service/loanPlanService";
import {LoanService} from "../../service/loanService";
import {NotificationService} from "../../../../core/service/notification.service";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {NewBorrowerComponent} from "../../../borrower/component/new-borrower/new-borrower.component";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {MetaData} from "../../../../core/model/metaData";

@Component({
  standalone: false,
  selector: 'app-create-loan',
  templateUrl: './create-loan.component.html',
  styleUrls: ['./create-loan.component.css']
})
export class CreateLoanComponent implements OnInit {

  loan: Loan;
  isEdit: boolean;
  customers: Array<Borrower> = [];
  searchKey: string;
  loanPlans: Array<LoanPlan> = [];
  statusList: Array<MetaData> = [];

  customerModal: BsModalRef;

  isSubmitting = false;

  constructor(private customerService: BorrowerService, private loanPlanService: LoanPlanService,
              private loanService: LoanService, private notificationService: NotificationService,
              private modalService: BsModalService,private metaDataService: MetaDataService) {
  }

  ngOnInit(): void {
    this.isSubmitting = false;
    this.loan = new Loan();
    this.getAllLoanPlans();
    this.findAllStatus();
  }

  clear() {
    this.isSubmitting = true;
    this.loan = new Loan();
  }

  loadCustomer() {
    if (this.searchKey !== '') {
      this.customerService.findByNameLike(this.searchKey).subscribe((data: Array<Borrower>) => {
        this.customers = data;
      });
    } else {
      this.ngOnInit();
    }
  }

  setSelectedCustomer(event) {
    this.loan.borrower = event.item;
  }

  saveLoan(loanForm: NgForm) {
    this.isSubmitting = true;
    if (null == this.loan.borrower || null == this.loan.loanPlan) {
      this.notificationService.showWarning("Please add necessary data");
      this.isSubmitting = false;
      return;
    }

    // Set status code based on selected status
    if (this.loan.status) {
      switch (this.loan.status.value) {
        case 'Pending':
          this.loan.statusCode = '1';
          break;
        case 'Issued':
          this.loan.statusCode = '2';
          break;
        case 'Current':
          this.loan.statusCode = '3';
          // Set next payment date if status is Current
          if (this.loan.loanPlan && this.loan.loanPlan.paymentFrequencyInDays) {
            const nextPaymentDate = new Date();
            nextPaymentDate.setDate(nextPaymentDate.getDate() + this.loan.loanPlan.paymentFrequencyInDays);
            this.loan.nextPaymentRecordDate = nextPaymentDate;
          }
          break;
        case 'Arrears':
          this.loan.statusCode = '4';
          break;
        case 'Settled':
          this.loan.statusCode = '5';
          break;
        default:
          this.loan.statusCode = '1'; // Default to Pending
      }
    } else {
      this.loan.statusCode = '1'; // Default to Pending if no status selected
    }

    this.loanService.save(this.loan).subscribe({
      next: (result) => {
        if (result.code === 200) {
          this.notificationService.showSuccess("Loan Created Successfully");
          this.ngOnInit();
          this.isSubmitting = false;
        } else {
          this.notificationService.showWarning("Creating Loan Failed: " + (result.message || 'Unknown error'));
          this.isSubmitting = false;
        }
      },
      error: (error) => {
        console.error('Error creating loan:', error);
        this.notificationService.showError("Error creating loan");
        this.isSubmitting = false;
      }
    });
  }

  getAllLoanPlans() {
    this.loanPlanService.findAllLoanPlan().subscribe((data: Array<LoanPlan>) => {
      this.loanPlans = data;
    })
  }

  setInstallment() {
    if (null != this.loan.loanPlan && this.loan.loanAmount) {
      this.loan.installmentAmount = this.loan.loanAmount / this.loan.loanPlan.totalNoOfInstallments;
    }
  }

  openCustomerModal() {
    this.customerModal = this.modalService.show(NewBorrowerComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalService.onHide.subscribe(event => {
      this.loan.borrower.id = this.customerModal.content.customer.id;
    })
  }

  findAllStatus() {
    this.metaDataService.findByCategory("Loan Status").subscribe((data: any) => {
      this.statusList = data;
    });
  }

}
