<div class="container-fluid p-3 bg-light min-vh-100">
  <!-- Header Section -->
  <div class="bg-primary text-white p-4 rounded-3 mb-4 shadow-sm">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <h4 class="mb-0">
        <i class="fas fa-money-bill-wave me-2"></i>
        Loan Payment
      </h4>
      <button type="button" class="btn btn-outline-light btn-sm" (click)="ngOnInit()">
        <i class="fas fa-sync-alt me-1"></i>
        Refresh
      </button>
    </div>
    <p class="mb-0 text-white-50">Process loan payments and manage borrower payment records.</p>
  </div>

  <!-- Search Section -->
  <div class="card mb-4 shadow-sm">
    <div class="card-header bg-light fw-semibold">
      <i class="fas fa-search me-2"></i> Search Borrower
    </div>
    <div class="card-body">
      <div class="row g-3 align-items-end">
        <div class="col-md-4">
          <input [(ngModel)]="keyNic" (ngModelChange)="clearSearch(1)"
                 placeholder="Search By NIC"
                 autocomplete="off"
                 class="form-control" name="loans">
        </div>
        <div class="col-md-3">
          <input [(ngModel)]="keyLoanNo" (ngModelChange)="clearSearch(2)"
                 placeholder="Search By Loan No"
                 autocomplete="off"
                 class="form-control" name="loans">
        </div>
        <div class="col-md-3">
          <input [(ngModel)]="keyTp1" (ngModelChange)="clearSearch(3)"
                 placeholder="Search By Telephone No"
                 autocomplete="off"
                 class="form-control" name="loans">
        </div>
        <div class="col-md-2 text-end">
          <button class="btn btn-primary w-100" (click)="search()" [disabled]="isSearching">
            <i class="fas fa-search me-1" [class.fa-spin]="isSearching"></i>
            {{ isSearching ? 'Searching...' : 'Search' }}
          </button>
        </div>
      </div>

      <div class="row mt-3">
        <div class="col-md-12">
          <button type="button" class="btn btn-outline-secondary" (click)="clearAllSearch()">
            <i class="fas fa-times me-1"></i> Clear All
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Active Loans Section -->
  <div class="card mb-4 shadow-sm" *ngIf="loans.length > 0">
    <div class="card-header bg-light fw-semibold">
      <i class="fas fa-file-invoice-dollar me-2"></i> Active Loans ({{loans.length}})
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover align-middle">
          <thead class="table-light">
          <tr>
            <th>Loan No</th>
            <th>Borrower</th>
            <th>Loan Plan</th>
            <th>Amount</th>
            <th>Balance</th>
            <th>Status</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let loan of loans">
            <td><strong>{{loan.loanNo}}</strong></td>
            <td>{{loan.borrower?.name}}</td>
            <td>{{loan.loanPlan?.name}}</td>
            <td>{{loan.loanAmount | currency:'LKR':'symbol':'1.2-2'}}</td>
            <td>{{loan.balance | currency:'LKR':'symbol':'1.2-2'}}</td>
            <td>
              <span class="badge" [ngClass]="{
                'bg-success': loan.status?.value === 'Current',
                'bg-warning': loan.status?.value === 'Issued',
                'bg-danger': loan.status?.value === 'Arrears',
                'bg-secondary': !loan.status
              }">
                {{loan.status?.value || 'Unknown'}}
              </span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Payment Records Table -->
  <div class="card mb-4 shadow-sm">
    <div class="card-header bg-light fw-semibold d-flex justify-content-between align-items-center">
      <div>
        <i class="fas fa-table me-2"></i> Payment Records
      </div>
      <span class="badge bg-info text-dark">{{ records?.length || 0 }} records</span>
    </div>
    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="table table-hover mb-0">
          <thead class="table-light text-center">
          <tr>
            <th>Date</th>
            <th>Installment</th>
            <th>Payment</th>
            <th>Balance</th>
            <th>Days Due</th>
            <th>Status</th>
          </tr>
          </thead>
          <tbody class="text-center align-middle">
          <tr *ngFor="let rec of records; let i = index"
              (click)="selectRow(rec, i)"
              [class.table-active]="i === selectedRow">
            <td>{{ rec.installmentDate | date: 'mediumDate' }}</td>
            <td>{{ rec.installmentAmount | number: '1.2-2' }}</td>
            <td>{{ rec.paidAmount | number: '1.2-2' }}</td>
            <td>{{ rec.balance | number: '1.2-2' }}</td>
            <td>
                <span class="badge"
                      [ngClass]="rec.daysDue > 0 ? 'bg-warning text-dark' : 'bg-success'">
                  {{ rec.daysDue }} days
                </span>
            </td>
            <td>
                <span class="badge" [ngClass]="getStatusBadgeClass(rec.status?.value)">
                  {{ rec.status?.value }}
                </span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Payment Form -->
  <div class="card shadow-sm" *ngIf="selectedRow !== null && selectedRecord">
    <div class="card-header bg-success text-white fw-semibold">
      <i class="fas fa-credit-card me-2"></i> Process Payment
    </div>
    <div class="card-body">
      <div class="row g-3">
        <div class="col-md-6">
          <h6 class="mb-3 text-primary">Selected Record Details:</h6>
          <div class="bg-light p-3 rounded">
            <p class="mb-2"><strong>Loan No:</strong> {{ selectedRecord?.loanNo }}</p>
            <p class="mb-2"><strong>Installment Amount:</strong>
              <span class="text-success fw-bold">{{ selectedRecord?.installmentAmount | currency:'LKR':'symbol':'1.2-2' }}</span>
            </p>
            <p class="mb-2"><strong>Current Balance:</strong>
              <span class="text-danger fw-bold">{{ selectedRecord?.balance | currency:'LKR':'symbol':'1.2-2' }}</span>
            </p>
            <p class="mb-0"><strong>Due Date:</strong>
              <span [class]="getDueDateClass(selectedRecord?.installmentDate)">
                {{ selectedRecord?.installmentDate | date: 'mediumDate' }}
              </span>
            </p>
          </div>
        </div>
        <div class="col-md-6">
          <h6 class="mb-3 text-primary">Payment Information:</h6>
          <div class="mb-3">
            <label for="paymentAmount" class="form-label">Payment Amount <span class="text-danger">*</span></label>
            <div class="input-group">
              <span class="input-group-text">LKR</span>
              <input id="paymentAmount"
                     type="number"
                     min="0.01"
                     step="0.01"
                     [(ngModel)]="payment"
                     placeholder="0.00"
                     class="form-control"
                     [class.is-invalid]="payment !== null && payment <= 0">
              <button class="btn btn-outline-secondary"
                      type="button"
                      (click)="payment = selectedRecord?.installmentAmount"
                      title="Set full installment amount">
                Full
              </button>
              <button class="btn btn-outline-secondary"
                      type="button"
                      (click)="payment = selectedRecord?.balance"
                      title="Set full balance amount">
                Balance
              </button>
            </div>
            <div class="form-text">
              Minimum: LKR 0.01 | Maximum: LKR {{ selectedRecord?.balance | number:'1.2-2' }}
            </div>
          </div>

          <div class="d-grid gap-2">
            <button class="btn btn-success btn-lg"
                    (click)="pay()"
                    [disabled]="!payment || payment <= 0 || payment > selectedRecord?.balance">
              <i class="fas fa-check me-2"></i> Process Payment
            </button>
            <button class="btn btn-outline-secondary"
                    (click)="clearPayment()">
              <i class="fas fa-times me-2"></i> Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
