import {Component, OnInit} from '@angular/core';
import {LoanPaymentService} from "../../service/loanPaymentService";
import {LoanService} from "../../service/loanService";
import {NotificationService} from "../../../../core/service/notification.service";
import {LoanRecordService} from "../../service/loanRecord.service";
import {BorrowerService} from "../../../borrower/service/borrower.service";
import {Loan} from "../../model/loan";
import {Borrower} from "../../../borrower/model/borrower";
import {LoanRecord} from "../../model/loanRecord";

@Component({
  standalone: false,
  selector: 'app-loan-payment',
  templateUrl: './loan-payment.component.html',
  styleUrls: ['./loan-payment.component.css']
})
export class LoanPaymentComponent implements OnInit {

  records: Array<LoanRecord> = [];
  loans: Array<Loan> = [];

  keyNic: string = '';
  keyLoanNo: string = '';
  keyTp1: string = '';

  borrowers: Array<Borrower> = [];
  selectedLoan: Loan;

  selectedRecord: LoanRecord;
  selectedRow: number;

  payment: number;
  isSearching: boolean = false;

  constructor(private loanService: LoanService,
              private notificationService: NotificationService,
              private loanRecordService: LoanRecordService,
              private borrowerService: BorrowerService) {
  }

  ngOnInit(): void {

  }

  search() {
    this.isSearching = true;
    this.loans = [];
    this.records = [];
    this.selectedLoan = null;
    this.selectedRecord = null;
    this.selectedRow = null;

    if (this.keyNic && this.keyNic.trim().length > 0) {
      this.searchByNic();
    } else if (this.keyLoanNo && this.keyLoanNo.trim().length > 0) {
      this.searchByLoanNo();
    } else if (this.keyTp1 && this.keyTp1.trim().length > 0) {
      this.searchByTelephone();
    } else {
      this.notificationService.showWarning("Please enter search criteria");
      this.isSearching = false;
    }
  }

  searchByNic() {
    this.borrowerService.findByNic(this.keyNic.trim()).subscribe({
      next: (borrower: Borrower) => {
        if (borrower) {
          this.findActiveLoansForBorrower(borrower.nic);
        } else {
          this.notificationService.showWarning("No borrower found with this NIC");
          this.isSearching = false;
        }
      },
      error: (error) => {
        console.error('Error finding borrower by NIC:', error);
        this.notificationService.showError("Error searching by NIC");
        this.isSearching = false;
      }
    });
  }

  searchByLoanNo() {
    this.loanService.findByLoanNo(this.keyLoanNo.trim()).subscribe({
      next: (loan: Loan) => {
        if (loan && this.isActiveLoan(loan)) {
          this.loans = [loan];
          this.loadPendingRecordsForLoan(loan.loanNo);
        } else if (loan) {
          this.notificationService.showWarning("This loan is not active for payments");
          this.isSearching = false;
        } else {
          this.notificationService.showWarning("No loan found with this number");
          this.isSearching = false;
        }
      },
      error: (error) => {
        console.error('Error finding loan by number:', error);
        this.notificationService.showError("Error searching by loan number");
        this.isSearching = false;
      }
    });
  }

  searchByTelephone() {
    this.borrowerService.findByTpLike(this.keyTp1.trim()).subscribe({
      next: (borrowers: Borrower[]) => {
        if (borrowers && borrowers.length > 0) {
          // If multiple borrowers found, get loans for all of them
          let processedCount = 0;
          borrowers.forEach(borrower => {
            this.loanService.findByNic(borrower.nic).subscribe({
              next: (loans: Loan[]) => {
                if (loans && loans.length > 0) {
                  const activeLoans = loans.filter(loan => this.isActiveLoan(loan));
                  this.loans = this.loans.concat(activeLoans);
                }
                processedCount++;
                if (processedCount === borrowers.length) {
                  if (this.loans.length > 0) {
                    this.loadPendingRecordsForLoans();
                  } else {
                    this.notificationService.showWarning("No active loans found for borrowers with this telephone");
                    this.isSearching = false;
                  }
                }
              },
              error: (error) => {
                console.error('Error finding loans for borrower:', error);
                processedCount++;
                if (processedCount === borrowers.length) {
                  this.isSearching = false;
                }
              }
            });
          });
        } else {
          this.notificationService.showWarning("No borrower found with this telephone");
          this.isSearching = false;
        }
      },
      error: (error) => {
        console.error('Error finding borrower by telephone:', error);
        this.notificationService.showError("Error searching by telephone");
        this.isSearching = false;
      }
    });
  }

  findActiveLoansForBorrower(nic: string) {
    this.loanService.findByNic(nic).subscribe({
      next: (loans: Loan[]) => {
        if (loans && loans.length > 0) {
          this.loans = loans.filter(loan => this.isActiveLoan(loan));
          if (this.loans.length > 0) {
            this.loadPendingRecordsForLoans();
          } else {
            this.notificationService.showWarning("No active loans found for this borrower");
            this.isSearching = false;
          }
        } else {
          this.notificationService.showWarning("No loans found for this borrower");
          this.isSearching = false;
        }
      },
      error: (error) => {
        console.error('Error finding loans for borrower:', error);
        this.notificationService.showError("Error finding loans");
        this.isSearching = false;
      }
    });
  }

  isActiveLoan(loan: Loan): boolean {
    // Check if loan status is Current (3) or Issued (2) or Arrears (4)
    return loan.statusCode === '2' || loan.statusCode === '3' || loan.statusCode === '4' ||
           (loan.status && ['Current', 'Issued', 'Arrears'].includes(loan.status.value));
  }

  loadPendingRecordsForLoans() {
    this.records = [];
    let processedCount = 0;

    this.loans.forEach(loan => {
      this.loadPendingRecordsForLoan(loan.loanNo, false);
      processedCount++;
      if (processedCount === this.loans.length) {
        this.isSearching = false;
      }
    });
  }

  loadPendingRecordsForLoan(loanNo: string, setSearchingFalse: boolean = true) {
    this.loanRecordService.findPendingRecordsByLoanNo(loanNo).subscribe({
      next: (records: LoanRecord[]) => {
        if (records && records.length > 0) {
          this.records = this.records.concat(records);
        }
        if (setSearchingFalse) {
          this.isSearching = false;
        }
      },
      error: (error) => {
        console.error('Error loading pending records for loan:', loanNo, error);
        if (setSearchingFalse) {
          this.isSearching = false;
        }
      }
    });
  }

  clearSearch(val) {
    if (val === 1) {
      this.keyTp1 = "";
      this.keyLoanNo = "";
    }
    if (val === 2) {
      this.keyTp1 = "";
      this.keyNic = "";
    }
    if (val === 3) {
      this.keyNic = "";
      this.keyLoanNo = "";
    }
    this.loans = [];
    this.records = [];
    this.selectedLoan = null;
    this.selectedRecord = null;
    this.selectedRow = null;
  }

  clearAllSearch() {
    this.keyNic = "";
    this.keyLoanNo = "";
    this.keyTp1 = "";
    this.loans = [];
    this.records = [];
    this.borrowers = [];
    this.selectedLoan = null;
    this.selectedRecord = null;
    this.selectedRow = null;
    this.payment = null;
  }

  selectRow(rec: LoanRecord, i: number) {
    this.selectedRecord = rec;
    this.selectedRow = i;
  }

  pay() {
    if (!this.selectedRecord) {
      this.notificationService.showWarning("Please select a loan record to pay");
      return;
    }

    if (!this.payment || this.payment <= 0) {
      this.notificationService.showWarning("Please enter a valid payment amount");
      return;
    }

    this.loanRecordService.pay(this.selectedRecord.id, this.payment).subscribe({
      next: (data: any) => {
        if (data.code === 200) {
          this.notificationService.showSuccess("Payment Successful: " + (data.message || 'Payment processed'));
          this.payment = null;
          this.selectedRecord = null;
          this.selectedRow = null;
          // Refresh the search results
          this.search();
        } else {
          this.notificationService.showError("Payment Failed: " + (data.message || 'Unknown error'));
        }
      },
      error: (error) => {
        console.error('Error processing payment:', error);
        this.notificationService.showError("Error processing payment");
      }
    });
  }

  getStatusBadgeClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'badge-success';
      case 'pending':
        return 'badge-warning';
      case 'overdue':
        return 'badge-danger';
      default:
        return 'badge-secondary';
    }
  }

  getDueDateClass(dueDate: Date): string {
    if (!dueDate) return '';

    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return 'text-danger fw-bold'; // Overdue
    } else if (diffDays <= 7) {
      return 'text-warning fw-bold'; // Due soon
    } else {
      return 'text-success'; // Not due yet
    }
  }

  clearPayment() {
    this.payment = null;
    this.selectedRecord = null;
    this.selectedRow = null;
  }

}
