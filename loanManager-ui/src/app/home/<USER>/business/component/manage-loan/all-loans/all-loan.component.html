<div class="container-fluid p-4 bg-light min-vh-100">
  <!-- Header Section -->
  <div class="bg-white border-start border-5 border-primary rounded shadow-sm p-4 mb-4">
    <div class="d-flex justify-content-between align-items-center">
      <h2 class="h4 fw-bold mb-0 text-primary">
        <i class="fas fa-list-alt me-2 text-warning"></i> All Loans
      </h2>
      <div class="btn-group">
        <button type="button" class="btn btn-outline-primary btn-sm" (click)="loadLoans()">
          <i class="fas fa-sync-alt me-1"></i> Refresh
        </button>
        <button type="button" class="btn btn-success btn-sm" routerLink="../create_loan">
          <i class="fas fa-plus me-1"></i> New Loan
        </button>
      </div>
    </div>
    <p class="text-muted mt-2 mb-0">View and manage all loans with advanced filtering options.</p>
  </div>

  <!-- Filters -->
  <div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white border-bottom">
      <h6 class="fw-semibold text-secondary mb-0">
        <i class="fas fa-filter text-primary me-2"></i> Search & Filter
      </h6>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <!-- Search by NIC with typeahead -->
        <div class="col-md-3">
          <div class="input-group">
            <span class="input-group-text bg-white border-end-0"><i class="fa fa-id-card text-muted"></i></span>
            <input
              [(ngModel)]="keyNic"
              [typeahead]="borrowers"
              (typeaheadLoading)="findByNic()"
              (typeaheadOnSelect)="setSelectedLoans($event)"
              [typeaheadOptionsLimit]="7"
              typeaheadOptionField="nic"
              autocomplete="off"
              class="form-control border-start-0"
              placeholder="Search By NIC"
              name="searchNic">
          </div>
        </div>

        <!-- Search by Name with Enter key -->
        <div class="col-md-3">
          <div class="input-group">
            <span class="input-group-text bg-white border-end-0"><i class="fa fa-user text-muted"></i></span>
            <input
              [(ngModel)]="keyName"
              (keyup.enter)="searchByName()"
              autocomplete="off"
              class="form-control border-start-0"
              placeholder="Search By Name (Press Enter)"
              name="searchName">
            <button class="btn btn-outline-primary" type="button" (click)="searchByName()">
              <i class="fa fa-search"></i>
            </button>
          </div>
        </div>

        <!-- Search by Telephone with Enter key -->
        <div class="col-md-3">
          <div class="input-group">
            <span class="input-group-text bg-white border-end-0"><i class="fa fa-phone text-muted"></i></span>
            <input
              [(ngModel)]="keyTp"
              (keyup.enter)="searchByTelephone()"
              autocomplete="off"
              class="form-control border-start-0"
              placeholder="Search By Telephone (Press Enter)"
              name="searchTp">
            <button class="btn btn-outline-primary" type="button" (click)="searchByTelephone()">
              <i class="fa fa-search"></i>
            </button>
          </div>
        </div>

        <!-- Search by Loan Number with Enter key -->
        <div class="col-md-3">
          <div class="input-group">
            <span class="input-group-text bg-white border-end-0"><i class="fa fa-file-text text-muted"></i></span>
            <input
              [(ngModel)]="keyLoanNo"
              (keyup.enter)="searchByLoanNumber()"
              autocomplete="off"
              class="form-control border-start-0"
              placeholder="Search By Loan No (Press Enter)"
              name="searchLoanNo">
            <button class="btn btn-outline-primary" type="button" (click)="searchByLoanNumber()">
              <i class="fa fa-search"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Second row for status filter and clear button -->
      <div class="row g-3 mt-2">
        <div class="col-md-3">
          <div class="input-group">
            <span class="input-group-text bg-white border-end-0"><i class="fa fa-filter text-muted"></i></span>
            <select class="form-select border-start-0"
                    name="loanPlan"
                    [(ngModel)]="selectedStatus"
                    (ngModelChange)="filterByStatus()">
              <option [ngValue]="null">All Status</option>
              <option *ngFor="let status of statusList" [ngValue]="status">{{status.value}}</option>
            </select>
          </div>
        </div>

        <div class="col-md-3">
          <button class="btn btn-outline-secondary w-100" type="button" (click)="clearAllFilters()">
            <i class="fa fa-times me-2"></i>Clear All Filters
          </button>
        </div>

        <div class="col-md-6" *ngIf="isSearching">
          <div class="alert alert-info mb-0">
            <i class="fa fa-spinner fa-spin me-2"></i>Searching...
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loans Table -->
  <div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
      <h6 class="fw-semibold text-secondary mb-0">
        <i class="fas fa-table text-primary me-2"></i> Loans List
      </h6>
      <span class="badge bg-primary">{{loanCount}} loans</span>
    </div>
    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="table table-hover align-middle text-center mb-0" id="print-section">
          <thead class="table-light">
          <tr>
            <th>Loan No</th>
            <th>Borrower Name</th>
            <th>Loan Plan</th>
            <th>Loan Amount</th>
            <th>Total Amount</th>
            <th>Loan Balance</th>
            <th>Paid Amount</th>
            <th>Status</th>
            <th>Loan Date</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let loan of loans; let i = index"
              (click)="selectRow(loan, i)"
              [class.table-active]="i === selectedRow">
            <td>{{loan.loanNo}}</td>
            <td>{{loan.borrower?.name || 'N/A'}}</td>
            <td>{{loan.loanPlan?.name || 'N/A'}}</td>
            <td>{{loan.loanAmount | number:'1.2-2'}}</td>
            <td>{{loan.loanAmountWithInterest | number:'1.2-2'}}</td>
            <td>{{loan.balance | number:'1.2-2'}}</td>
            <td>{{loan.paidAmount}}</td>
            <td>{{loan.status?.value || 'N/A'}}</td>
            <td>{{loan.dateTime | date}}</td>
          </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="p-3 d-flex justify-content-center">
        <pagination class="pagination-sm"
                    [totalItems]="collectionSize"
                    [(ngModel)]="page"
                    [maxSize]="15"
                    [boundaryLinks]="true"
                    (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
    </div>
  </div>

  <!-- Summary Section -->
  <div class="card border-0 shadow-sm">
    <div class="card-header bg-success text-white">
      <h6 class="mb-0 fw-semibold">
        <i class="fas fa-chart-bar me-2"></i> Loan Summary
      </h6>
    </div>
    <div class="card-body">
      <div class="row g-3 align-items-end">
        <div class="col-md-3">
          <div class="d-flex justify-content-between">
            <span class="text-muted">Loan Count:</span>
            <span class="fw-bold fs-5">{{loanCount}}</span>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex justify-content-between">
            <span class="text-muted">Total Amount:</span>
            <span class="fw-bold fs-5 text-primary">{{totalAmount | number:'1.2-2'}}</span>
          </div>
        </div>
        <div class="col-md-3">
          <div class="d-flex justify-content-between">
            <span class="text-muted">Total Collectable:</span>
            <span class="fw-bold fs-5 text-success">{{totalCollectable | number:'1.2-2'}}</span>
          </div>
        </div>
        <div class="col-md-3 text-end">
          <div class="btn-group">
            <button class="btn btn-outline-primary btn-sm" printSectionId="print-section" #printBtn ngxPrint="useExistingCss">
              <i class="fas fa-print me-1"></i> Print
            </button>
            <button class="btn btn-primary btn-sm" type="button" (click)="loansDetail()" [disabled]="selectedRow===null">
              <i class="fas fa-eye me-1"></i> Details
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
