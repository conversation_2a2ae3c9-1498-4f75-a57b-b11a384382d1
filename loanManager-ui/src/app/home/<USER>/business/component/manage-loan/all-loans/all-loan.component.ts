import {Component, OnInit} from '@angular/core';
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {Loan} from "../../../model/loan";
import {LoanService} from "../../../service/loanService";
import {LoanDetailsComponent} from "../../loan-details/loan-details.component";
import {BorrowerService} from "../../../../borrower/service/borrower.service";
import {<PERSON>rrow<PERSON>} from "../../../../borrower/model/borrower";
import {MetaData} from "../../../../../core/model/metaData";
import {MetaDataService} from "../../../../../core/service/metaData.service";

@Component({
  standalone: false,
  selector: 'app-manage-loan',
  templateUrl: './all-loan.component.html',
  styleUrls: ['./all-loan.component.css']
})
export class AllLoanComponent implements OnInit {

  modalRef: BsModalRef;
  loans: Array<Loan> = [];
  borrowers: Array<Borrower> = [];
  selectedLoan: Loan;

  statusList: Array<MetaData> = [];
  selectedStatus: MetaData = null;
  isSearching: boolean = false;

  selectedRow: number;

  keyNic: string;
  keyName: string;
  keyTp: string;
  keyLoanNo: string;

  loanCount: number;
  totalAmount: number;
  totalCollectable: number;

  page;
  collectionSize;
  pageSize;

  constructor(private loanService: LoanService, private modalService: BsModalService,
              private borrowerService: BorrowerService, private metaDataService: MetaDataService) {
  }

  ngOnInit(): void {
    this.page = 1;
    this.pageSize = 10;
    this.loadLoans();
    this.findAllStatus();
  }

  loadLoans() {
    this.loans = [];
    this.loanService.getAllLoans(this.page - 1, this.pageSize).subscribe({
      next: (data: any) => {
        this.loans = data?.content || [];
        this.collectionSize = data?.totalElements || 0;
        this.calculateTotal();
      },
      error: (error) => {
        console.error('Error loading loans:', error);
        this.loans = [];
        this.collectionSize = 0;
      }
    });
  }

  findAllStatus() {
    this.metaDataService.findByCategory("Loan Status").subscribe({
      next: (data: any) => {
        this.statusList = data || [];
      },
      error: (error) => {
        console.error('Error loading loan statuses:', error);
        this.statusList = [];
      }
    });
  }

  filterByStatus() {
    if (this.selectedStatus && this.selectedStatus.id) {
      this.loanService.findByStatus(this.selectedStatus.id, this.page - 1, this.pageSize).subscribe({
        next: (data: any) => {
          this.loans = data?.content || [];
          this.collectionSize = data?.totalElements || 0;
          this.calculateTotal();
        },
        error: (error) => {
          console.error('Error filtering loans by status:', error);
          this.loans = [];
          this.collectionSize = 0;
        }
      });
    } else {
      // If no status selected, load all loans
      this.loadLoans();
    }
  }

  findByNic = () => {
    if (this.keyNic && this.keyNic.length > 2) {
      this.borrowerService.findByNic(this.keyNic).subscribe({
        next: (data: any) => {
          this.borrowers = data ? [data] : [];
        },
        error: (error) => {
          console.error('Error finding borrower by NIC:', error);
          this.borrowers = [];
        }
      });
    }
  }

  searchByName() {
    if (this.keyName && this.keyName.trim().length >= 2) {
      this.isSearching = true;
      this.borrowerService.findByNameLike(this.keyName.trim()).subscribe({
        next: (data: any) => {
          if (data && data.length > 0) {
            // Get loans for all matching borrowers
            this.loans = [];
            let processedCount = 0;
            data.forEach((borrower: any) => {
              this.loanService.findByNic(borrower.nic).subscribe({
                next: (loanData: Array<Loan>) => {
                  if (loanData && loanData.length > 0) {
                    this.loans = this.loans.concat(loanData);
                  }
                  processedCount++;
                  if (processedCount === data.length) {
                    this.calculateTotal();
                    this.isSearching = false;
                  }
                },
                error: (error) => {
                  console.error('Error finding loans for borrower:', error);
                  processedCount++;
                  if (processedCount === data.length) {
                    this.calculateTotal();
                    this.isSearching = false;
                  }
                }
              });
            });
          } else {
            this.loans = [];
            this.calculateTotal();
            this.isSearching = false;
          }
        },
        error: (error) => {
          console.error('Error finding borrower by name:', error);
          this.loans = [];
          this.isSearching = false;
        }
      });
    } else if (this.keyName && this.keyName.trim().length > 0 && this.keyName.trim().length < 2) {
      alert('Please enter at least 2 characters to search by name.');
    }
  }

  searchByTelephone() {
    if (this.keyTp && this.keyTp.trim().length >= 3) {
      this.isSearching = true;
      this.borrowerService.findByTpLike(this.keyTp.trim()).subscribe({
        next: (data: any) => {
          if (data && data.length > 0) {
            // Get loans for all matching borrowers
            this.loans = [];
            let processedCount = 0;
            data.forEach((borrower: any) => {
              this.loanService.findByNic(borrower.nic).subscribe({
                next: (loanData: Array<Loan>) => {
                  if (loanData && loanData.length > 0) {
                    this.loans = this.loans.concat(loanData);
                  }
                  processedCount++;
                  if (processedCount === data.length) {
                    this.calculateTotal();
                    this.isSearching = false;
                  }
                },
                error: (error) => {
                  console.error('Error finding loans for borrower:', error);
                  processedCount++;
                  if (processedCount === data.length) {
                    this.calculateTotal();
                    this.isSearching = false;
                  }
                }
              });
            });
          } else {
            this.loans = [];
            this.calculateTotal();
            this.isSearching = false;
          }
        },
        error: (error) => {
          console.error('Error finding borrower by telephone:', error);
          this.loans = [];
          this.isSearching = false;
        }
      });
    } else if (this.keyTp && this.keyTp.trim().length > 0 && this.keyTp.trim().length < 3) {
      alert('Please enter at least 3 characters to search by telephone.');
    }
  }

  searchByLoanNumber() {
    if (this.keyLoanNo && this.keyLoanNo.trim().length >= 2) {
      this.isSearching = true;
      this.loanService.findByLoanNo(this.keyLoanNo.trim()).subscribe({
        next: (data: Loan) => {
          this.loans = data ? [data] : [];
          this.calculateTotal();
          this.isSearching = false;
        },
        error: (error) => {
          console.error('Error finding loan by loan number:', error);
          this.loans = [];
          this.isSearching = false;
        }
      });
    } else if (this.keyLoanNo && this.keyLoanNo.trim().length > 0 && this.keyLoanNo.trim().length < 2) {
      alert('Please enter at least 2 characters to search by loan number.');
    }
  }

  clearAllFilters() {
    this.keyNic = '';
    this.keyName = '';
    this.keyTp = '';
    this.keyLoanNo = '';
    this.selectedStatus = null;
    this.borrowers = [];
    this.loadLoans(); // Reload all loans
  }

  selectRow(loan: Loan, i: number) {
    this.selectedLoan = loan;
    this.selectedRow = i;
  }

  setSelectedLoans(event) {
    if (event && event.item && event.item.nic) {
      this.loanService.findByNic(event.item.nic).subscribe({
        next: (data: Array<Loan>) => {
          this.loans = data || [];
          this.calculateTotal();
        },
        error: (error) => {
          console.error('Error finding loans by NIC:', error);
          this.loans = [];
        }
      });
    }
  }

  loansDetail() {
    if (this.selectedLoan !== null) {
      this.modalRef = this.modalService.show(LoanDetailsComponent, <ModalOptions>{class: 'modal-xl'});
      this.modalRef.content.loan = this.selectedLoan;
      this.modalRef.content.findLoanRecord(this.selectedLoan.loanNo);
    }
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.loadLoans();
  }

  calculateTotal() {
    this.loanCount = 0;
    this.totalAmount = 0;
    this.totalCollectable = 0;

    for (let ln of this.loans) {
      this.loanCount = this.loanCount + 1;
      this.totalAmount = this.totalAmount + ln.loanAmount;
      this.totalCollectable = this.totalCollectable + ln.loanAmountWithInterest;
    }
  }

}
