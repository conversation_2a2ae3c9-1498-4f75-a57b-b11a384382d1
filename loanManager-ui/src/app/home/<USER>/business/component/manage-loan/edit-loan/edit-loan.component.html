<div class="container-fluid p-4 bg-light min-vh-100">
  <!-- Header Section -->
  <div class="bg-white border-start border-5 border-primary rounded shadow-sm p-4 mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <h2 class="h4 text-primary fw-bold mb-0">
        <i class="fas fa-edit me-2"></i> Edit Loan
      </h2>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-secondary btn-sm" (click)="clearLoan()">
          <i class="fas fa-plus me-1"></i> New Search
        </button>
        <button class="btn btn-outline-info btn-sm" (click)="toggleAdvancedFields()">
          <i class="fas fa-cog me-1"></i> {{ showAdvancedFields ? 'Hide' : 'Show' }} Advanced
        </button>
      </div>
    </div>
    <p class="text-muted mb-0">Search and modify all important properties of loan records.</p>

    <!-- Unsaved Changes Warning -->
    <div class="alert alert-warning mt-3 mb-0" *ngIf="hasUnsavedChanges">
      <i class="fas fa-exclamation-triangle me-2"></i>
      <strong>Warning:</strong> You have unsaved changes. Please save or discard them before proceeding.
    </div>
  </div>

  <!-- Search Section -->
  <div class="card border-0 shadow-sm mb-4" *ngIf="!hideSearchSection">
    <div class="card-header bg-white border-bottom">
      <h6 class="fw-semibold text-secondary mb-0">
        <i class="fas fa-search text-primary me-2"></i> Search Loan
      </h6>
    </div>
    <div class="card-body">
      <div class="row g-3 align-items-end">
        <div class="col-md-8">
          <label for="searchLoanNo" class="form-label">Loan Number <span class="text-danger">*</span></label>
          <input
            id="searchLoanNo"
            type="text"
            class="form-control"
            [(ngModel)]="searchLoanNo"
            placeholder="Enter loan number to search"
            (keyup.enter)="searchLoan()"
            [disabled]="isSearching">
        </div>
        <div class="col-md-4">
          <button class="btn btn-primary w-100"
                  (click)="searchLoan()"
                  [disabled]="isSearching || !searchLoanNo">
            <i class="fas fa-search me-1" [class.fa-spin]="isSearching"></i>
            {{ isSearching ? 'Searching...' : 'Search Loan' }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Loan Edit Form -->
  <div class="card border-0 shadow-sm" *ngIf="isLoanLoaded">
    <div class="card-header bg-white border-bottom">
      <div class="d-flex justify-content-between align-items-center">
        <h6 class="fw-semibold text-secondary mb-0">
          <i class="fas fa-file-invoice-dollar text-primary me-2"></i>
          Loan Details - {{loan.loanNo}}
        </h6>
        <span class="badge" [ngClass]="getStatusBadgeClass(loan.status?.value)">
          {{loan.status?.value || 'No Status'}}
        </span>
      </div>
    </div>
    <div class="card-body">
      <form #loanForm="ngForm" (ngSubmit)="saveLoan(loanForm)">

        <!-- Basic Information -->
        <div class="row g-3 mb-4">
          <div class="col-12">
            <h6 class="text-primary fw-semibold border-bottom pb-2 mb-3">
              <i class="fas fa-info-circle me-2"></i> Basic Information
            </h6>
          </div>

          <div class="col-md-4">
            <label for="loanNo" class="form-label fw-semibold">Loan Number</label>
            <input
              id="loanNo"
              type="text"
              class="form-control"
              [(ngModel)]="loan.loanNo"
              name="loanNo"
              readonly
              style="background-color: #f8f9fa;">
          </div>

          <div class="col-md-4">
            <label for="dateTime" class="form-label fw-semibold">Issue Date</label>
            <input
              id="dateTime"
              type="date"
              class="form-control"
              [(ngModel)]="loan.dateTime"
              name="dateTime"
              (change)="markAsChanged()">
          </div>

          <div class="col-md-4">
            <label for="status" class="form-label fw-semibold">Status <span class="text-danger">*</span></label>
            <select
              id="status"
              class="form-select"
              [(ngModel)]="loan.status"
              name="status"
              required
              (change)="onStatusChange()">
              <option [ngValue]="null">Select Status</option>
              <option *ngFor="let status of statusList" [ngValue]="status">{{status.value}}</option>
            </select>
          </div>
        </div>

        <!-- Borrower Information -->
        <div class="row g-3 mb-4">
          <div class="col-12">
            <h6 class="text-primary fw-semibold border-bottom pb-2 mb-3">
              <i class="fas fa-user me-2"></i> Borrower Information
            </h6>
          </div>

          <div class="col-md-6" *ngIf="loan.borrower?.name">
            <div class="bg-light p-3 rounded">
              <h6 class="mb-2">Current Borrower:</h6>
              <p class="mb-1"><strong>Name:</strong> {{loan.borrower.name}}</p>
              <p class="mb-1"><strong>NIC:</strong> {{loan.borrower.nic}}</p>
              <p class="mb-0"><strong>Phone:</strong> {{loan.borrower.telephone1}}</p>
            </div>
          </div>

          <div class="col-md-6">
            <div class="mb-3">
              <label for="searchBorrowerNic" class="form-label">Search by NIC</label>
              <div class="input-group">
                <input
                  id="searchBorrowerNic"
                  type="text"
                  class="form-control"
                  [(ngModel)]="searchBorrowerNic"
                  name="searchBorrowerNic"
                  placeholder="Enter NIC"
                  (keyup.enter)="searchBorrowerByNic()">
                <button class="btn btn-outline-primary" type="button" (click)="searchBorrowerByNic()">
                  <i class="fas fa-search"></i>
                </button>
              </div>
            </div>

            <div class="mb-3">
              <label for="searchBorrowerName" class="form-label">Search by Name</label>
              <div class="input-group">
                <input
                  id="searchBorrowerName"
                  type="text"
                  class="form-control"
                  [(ngModel)]="searchBorrowerName"
                  name="searchBorrowerName"
                  placeholder="Enter name"
                  (keyup.enter)="searchBorrowerByName()">
                <button class="btn btn-outline-primary" type="button" (click)="searchBorrowerByName()">
                  <i class="fas fa-search"></i>
                </button>
              </div>
            </div>

            <!-- Borrower Search Results -->
            <div class="list-group" *ngIf="borrowers.length > 0">
              <div class="list-group-item list-group-item-action"
                   *ngFor="let borrower of borrowers"
                   (click)="selectBorrower(borrower)"
                   style="cursor: pointer;">
                <div class="d-flex justify-content-between">
                  <div>
                    <h6 class="mb-1">{{borrower.name}}</h6>
                    <p class="mb-1">NIC: {{borrower.nic}}</p>
                    <small>Phone: {{borrower.telephone1}}</small>
                  </div>
                  <small class="text-muted">Click to select</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Loan Plan and Amount -->
        <div class="row g-3 mb-4">
          <div class="col-12">
            <h6 class="text-primary fw-semibold border-bottom pb-2 mb-3">
              <i class="fas fa-calculator me-2"></i> Loan Plan & Amount
            </h6>
          </div>

          <div class="col-md-6">
            <label for="loanPlan" class="form-label fw-semibold">Loan Plan <span class="text-danger">*</span></label>
            <select
              id="loanPlan"
              class="form-select"
              [(ngModel)]="loan.loanPlan"
              name="loanPlan"
              required
              (change)="onLoanPlanChange()">
              <option [ngValue]="null">Select Loan Plan</option>
              <option *ngFor="let plan of loanPlans" [ngValue]="plan">{{plan.name}}</option>
            </select>
          </div>

          <div class="col-md-6">
            <label for="loanAmount" class="form-label fw-semibold">Loan Amount <span class="text-danger">*</span></label>
            <div class="input-group">
              <span class="input-group-text">LKR</span>
              <input
                id="loanAmount"
                type="number"
                class="form-control"
                [(ngModel)]="loan.loanAmount"
                name="loanAmount"
                required
                min="0"
                step="0.01"
                (input)="onAmountChange()">
            </div>
          </div>

          <div class="col-md-6">
            <label for="installmentAmount" class="form-label fw-semibold">Installment Amount</label>
            <div class="input-group">
              <span class="input-group-text">LKR</span>
              <input
                id="installmentAmount"
                type="number"
                class="form-control"
                [(ngModel)]="loan.installmentAmount"
                name="installmentAmount"
                step="0.01"
                (input)="markAsChanged()">
            </div>
          </div>

          <div class="col-md-6">
            <label for="loanAmountWithInterest" class="form-label fw-semibold">Total Amount (with Interest)</label>
            <div class="input-group">
              <span class="input-group-text">LKR</span>
              <input
                id="loanAmountWithInterest"
                type="number"
                class="form-control"
                [(ngModel)]="loan.loanAmountWithInterest"
                name="loanAmountWithInterest"
                step="0.01"
                (input)="markAsChanged()">
            </div>
          </div>
        </div>

        <!-- Payment Information -->
        <div class="row g-3 mb-4">
          <div class="col-12">
            <h6 class="text-primary fw-semibold border-bottom pb-2 mb-3">
              <i class="fas fa-money-bill-wave me-2"></i> Payment Information
            </h6>
          </div>

          <div class="col-md-4">
            <label for="balance" class="form-label fw-semibold">Current Balance</label>
            <div class="input-group">
              <span class="input-group-text">LKR</span>
              <input
                id="balance"
                type="number"
                class="form-control"
                [(ngModel)]="loan.balance"
                name="balance"
                step="0.01"
                (input)="markAsChanged()">
            </div>
          </div>

          <div class="col-md-4">
            <label for="paidAmount" class="form-label fw-semibold">Paid Amount</label>
            <div class="input-group">
              <span class="input-group-text">LKR</span>
              <input
                id="paidAmount"
                type="number"
                class="form-control"
                [(ngModel)]="loan.paidAmount"
                name="paidAmount"
                step="0.01"
                (input)="markAsChanged()">
            </div>
          </div>

          <div class="col-md-4">
            <label for="installmentLeft" class="form-label fw-semibold">Installments Left</label>
            <input
              id="installmentLeft"
              type="number"
              class="form-control"
              [(ngModel)]="loan.installmentLeft"
              name="installmentLeft"
              min="0"
              (input)="markAsChanged()">
          </div>
        </div>

        <!-- Advanced Fields -->
        <div class="row g-3 mb-4" *ngIf="showAdvancedFields">
          <div class="col-12">
            <h6 class="text-warning fw-semibold border-bottom pb-2 mb-3">
              <i class="fas fa-cogs me-2"></i> Advanced Fields
            </h6>
          </div>

          <div class="col-md-4">
            <label for="arrearsAmount" class="form-label fw-semibold">Arrears Amount</label>
            <div class="input-group">
              <span class="input-group-text">LKR</span>
              <input
                id="arrearsAmount"
                type="number"
                class="form-control"
                [(ngModel)]="loan.arrearsAmount"
                name="arrearsAmount"
                step="0.01"
                (input)="markAsChanged()">
            </div>
          </div>

          <div class="col-md-4">
            <label for="arrearsRecordCount" class="form-label fw-semibold">Arrears Record Count</label>
            <input
              id="arrearsRecordCount"
              type="number"
              class="form-control"
              [(ngModel)]="loan.arrearsRecordCount"
              name="arrearsRecordCount"
              min="0"
              (input)="markAsChanged()">
          </div>

          <div class="col-md-4">
            <label for="overPaidAmount" class="form-label fw-semibold">Over Paid Amount</label>
            <div class="input-group">
              <span class="input-group-text">LKR</span>
              <input
                id="overPaidAmount"
                type="number"
                class="form-control"
                [(ngModel)]="loan.overPaidAmount"
                name="overPaidAmount"
                step="0.01"
                (input)="markAsChanged()">
            </div>
          </div>

          <div class="col-md-6">
            <label for="nextPaymentRecordDate" class="form-label fw-semibold">Next Payment Date</label>
            <input
              id="nextPaymentRecordDate"
              type="date"
              class="form-control"
              [(ngModel)]="loan.nextPaymentRecordDate"
              name="nextPaymentRecordDate"
              (change)="markAsChanged()">
          </div>

          <div class="col-md-6">
            <label for="settlementDate" class="form-label fw-semibold">Settlement Date</label>
            <input
              id="settlementDate"
              type="date"
              class="form-control"
              [(ngModel)]="loan.settlementDate"
              name="settlementDate"
              (change)="markAsChanged()">
          </div>

          <div class="col-md-6">
            <label for="approvedBy" class="form-label fw-semibold">Approved By</label>
            <input
              id="approvedBy"
              type="text"
              class="form-control"
              [(ngModel)]="loan.approvedBy"
              name="approvedBy"
              (input)="markAsChanged()">
          </div>

          <div class="col-md-6">
            <label for="approvedDate" class="form-label fw-semibold">Approved Date</label>
            <input
              id="approvedDate"
              type="date"
              class="form-control"
              [(ngModel)]="loan.approvedDate"
              name="approvedDate"
              (change)="markAsChanged()">
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="d-flex justify-content-end gap-2 mt-4">
          <button type="button"
                  class="btn btn-outline-warning"
                  (click)="resetChanges()"
                  [disabled]="!hasUnsavedChanges">
            <i class="fas fa-undo me-1"></i> Reset Changes
          </button>
          <button type="button"
                  class="btn btn-outline-secondary"
                  (click)="clearLoan()">
            <i class="fas fa-times me-1"></i> Clear
          </button>
          <button type="submit"
                  class="btn btn-success"
                  [disabled]="!loanForm.form.valid || isSubmitting || !hasUnsavedChanges">
            <i class="fas fa-save me-1" [class.fa-spin]="isSubmitting"></i>
            {{ isSubmitting ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
