import {Component, OnInit} from '@angular/core';
import {<PERSON>an} from "../../../model/loan";
import {<PERSON><PERSON><PERSON>} from "../../../../borrower/model/borrower";
import {LoanPlan} from "../../../model/loanPlan";
import {MetaData} from "../../../../../core/model/metaData";
import {LoanService} from "../../../service/loanService";
import {BorrowerService} from "../../../../borrower/service/borrower.service";
import {LoanPlanService} from "../../../service/loanPlanService";
import {NotificationService} from "../../../../../core/service/notification.service";
import {NgForm} from "@angular/forms";
import {MetaDataService} from "../../../../../core/service/metaData.service";

@Component({
  standalone: false,
  selector: 'app-edit-loan',
  templateUrl: './edit-loan.component.html',
  styleUrls: ['./edit-loan.component.css']
})
export class EditLoanComponent implements OnInit {

  loan: Loan = new Loan();
  originalLoan: Loan = new Loan();

  // Search and selection
  searchLoanNo: string = '';
  searchBorrowerNic: string = '';
  searchBorrowerName: string = '';

  // Data lists
  borrowers: Array<Borrower> = [];
  loanPlans: Array<LoanPlan> = [];
  statusList: Array<MetaData> = [];

  // UI state
  isLoading: boolean = false;
  isSearching: boolean = false;
  isSubmitting: boolean = false;
  isLoanLoaded: boolean = false;
  showAdvancedFields: boolean = false;

  // Validation flags
  hasUnsavedChanges: boolean = false;

  constructor(
    private loanService: LoanService,
    private borrowerService: BorrowerService,
    private loanPlanService: LoanPlanService,
    private metaDataService: MetaDataService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadInitialData();
  }

  loadInitialData() {
    this.loadLoanPlans();
    this.loadStatusList();
  }

  loadLoanPlans() {
    this.loanPlanService.findAllLoanPlan().subscribe({
      next: (data: Array<LoanPlan>) => {
        this.loanPlans = data || [];
      },
      error: (error) => {
        console.error('Error loading loan plans:', error);
        this.notificationService.showError('Failed to load loan plans');
      }
    });
  }

  loadStatusList() {
    this.metaDataService.findByCategory("Loan Status").subscribe({
      next: (data: Array<MetaData>) => {
        this.statusList = data || [];
      },
      error: (error) => {
        console.error('Error loading status list:', error);
        this.notificationService.showError('Failed to load status list');
      }
    });
  }

  searchLoan() {
    if (!this.searchLoanNo || this.searchLoanNo.trim().length === 0) {
      this.notificationService.showWarning('Please enter a loan number');
      return;
    }

    this.isSearching = true;
    this.loanService.findByLoanNo(this.searchLoanNo.trim()).subscribe({
      next: (data: Loan) => {
        if (data) {
          this.loan = { ...data };
          this.originalLoan = { ...data };
          this.isLoanLoaded = true;
          this.hasUnsavedChanges = false;
          this.notificationService.showSuccess('Loan loaded successfully');
        } else {
          this.notificationService.showWarning('No loan found with this number');
          this.clearLoan();
        }
        this.isSearching = false;
      },
      error: (error) => {
        console.error('Error searching loan:', error);
        this.notificationService.showError('Error searching for loan');
        this.isSearching = false;
      }
    });
  }

  searchBorrowerByNic() {
    if (!this.searchBorrowerNic || this.searchBorrowerNic.trim().length < 5) {
      this.notificationService.showWarning('Please enter at least 5 characters for NIC');
      return;
    }

    this.borrowerService.findByNic(this.searchBorrowerNic.trim()).subscribe({
      next: (data: Borrower) => {
        if (data) {
          this.loan.borrower = data;
          this.markAsChanged();
        } else {
          this.notificationService.showWarning('No borrower found with this NIC');
        }
      },
      error: (error) => {
        console.error('Error searching borrower by NIC:', error);
        this.notificationService.showError('Error searching borrower');
      }
    });
  }

  searchBorrowerByName() {
    if (!this.searchBorrowerName || this.searchBorrowerName.trim().length < 2) {
      this.notificationService.showWarning('Please enter at least 2 characters for name');
      return;
    }

    this.borrowerService.findByNameLike(this.searchBorrowerName.trim()).subscribe({
      next: (data: Array<Borrower>) => {
        if (data && data.length > 0) {
          this.borrowers = data;
          if (data.length === 1) {
            this.loan.borrower = data[0];
            this.markAsChanged();
          }
        } else {
          this.notificationService.showWarning('No borrowers found with this name');
          this.borrowers = [];
        }
      },
      error: (error) => {
        console.error('Error searching borrower by name:', error);
        this.notificationService.showError('Error searching borrower');
      }
    });
  }

  selectBorrower(borrower: Borrower) {
    this.loan.borrower = borrower;
    this.borrowers = [];
    this.searchBorrowerName = '';
    this.markAsChanged();
  }

  onLoanPlanChange() {
    if (this.loan.loanPlan && this.loan.loanAmount) {
      this.calculateInstallmentAmount();
    }
    this.markAsChanged();
  }

  onStatusChange() {
    if (this.loan.status) {
      // Set status code based on selected status
      switch (this.loan.status.value) {
        case 'Pending':
          this.loan.statusCode = '1';
          break;
        case 'Issued':
          this.loan.statusCode = '2';
          break;
        case 'Current':
          this.loan.statusCode = '3';
          // Set next payment date if status is Current
          if (this.loan.loanPlan && this.loan.loanPlan.paymentFrequencyInDays) {
            const nextPaymentDate = new Date();
            nextPaymentDate.setDate(nextPaymentDate.getDate() + this.loan.loanPlan.paymentFrequencyInDays);
            this.loan.nextPaymentRecordDate = nextPaymentDate;
          }
          break;
        case 'Arrears':
          this.loan.statusCode = '4';
          break;
        case 'Settled':
          this.loan.statusCode = '5';
          this.loan.active = false;
          break;
        default:
          this.loan.statusCode = '1';
      }
    }
    this.markAsChanged();
  }

  calculateInstallmentAmount() {
    if (this.loan.loanPlan && this.loan.loanAmount) {
      this.loan.installmentAmount = this.loan.loanAmount / this.loan.loanPlan.totalNoOfInstallments;

      // Calculate loan amount with interest
      const interestAmount = (this.loan.loanAmount * this.loan.loanPlan.interestRate / 100 / 30) * this.loan.loanPlan.durationInDays;
      this.loan.loanAmountWithInterest = this.loan.loanAmount + interestAmount;
    }
    this.markAsChanged();
  }

  onAmountChange() {
    this.calculateInstallmentAmount();
    this.markAsChanged();
  }

  markAsChanged() {
    this.hasUnsavedChanges = true;
  }

  saveLoan(loanForm: NgForm) {
    if (!this.isLoanLoaded) {
      this.notificationService.showWarning('Please load a loan first');
      return;
    }

    if (!loanForm.valid) {
      this.notificationService.showWarning('Please fill in all required fields correctly');
      return;
    }

    this.isSubmitting = true;

    this.loanService.update(this.loan).subscribe({
      next: (result) => {
        if (result.code === 200) {
          this.notificationService.showSuccess('Loan updated successfully');
          this.originalLoan = { ...this.loan };
          this.hasUnsavedChanges = false;
        } else {
          this.notificationService.showError('Failed to update loan: ' + (result.message || 'Unknown error'));
        }
        this.isSubmitting = false;
      },
      error: (error) => {
        console.error('Error updating loan:', error);
        this.notificationService.showError('Error updating loan');
        this.isSubmitting = false;
      }
    });
  }

  resetChanges() {
    if (this.hasUnsavedChanges) {
      if (confirm('Are you sure you want to discard all changes?')) {
        this.loan = { ...this.originalLoan };
        this.hasUnsavedChanges = false;
        this.notificationService.showInfo('Changes discarded');
      }
    }
  }

  clearLoan() {
    this.loan = new Loan();
    this.originalLoan = new Loan();
    this.isLoanLoaded = false;
    this.hasUnsavedChanges = false;
    this.borrowers = [];
    this.searchLoanNo = '';
    this.searchBorrowerNic = '';
    this.searchBorrowerName = '';
  }

  toggleAdvancedFields() {
    this.showAdvancedFields = !this.showAdvancedFields;
  }

  getStatusBadgeClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'current':
        return 'bg-success';
      case 'pending':
        return 'bg-warning';
      case 'issued':
        return 'bg-info';
      case 'arrears':
        return 'bg-danger';
      case 'settled':
        return 'bg-secondary';
      default:
        return 'bg-light text-dark';
    }
  }
}
