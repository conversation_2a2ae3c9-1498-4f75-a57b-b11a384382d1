import {Component, OnInit} from '@angular/core';
import {RejectedLoan} from "../../../model/rejectedLoan";
import {RejectedLoanService} from "../../../service/rejectedLoanService";
import {NotificationService} from "../../../../../core/service/notification.service";

@Component({
  standalone: false,
  selector: 'app-rejected-loans',
  templateUrl: './rejected-loans.component.html',
  styleUrls: ['./rejected-loans.component.css']
})
export class RejectedLoansComponent implements OnInit {

  rejectedLoans: Array<RejectedLoan> = [];
  selectedLoan: RejectedLoan;
  selectedRow: number;

  page: number = 1;
  collectionSize: number = 0;
  pageSize: number = 10;

  // Search fields
  searchCustomerName: string = '';
  searchCustomerNic: string = '';
  searchTelephone: string = '';
  isSearching: boolean = false;

  constructor(
    private rejectedLoanService: RejectedLoanService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadRejectedLoans();
  }

  loadRejectedLoans() {
    this.rejectedLoanService.getAllRejectedLoans(this.page - 1, this.pageSize).subscribe({
      next: (data: any) => {
        this.rejectedLoans = data?.content || [];
        this.collectionSize = data?.totalElements || 0;
      },
      error: (error) => {
        console.error('Error loading rejected loans:', error);
        this.notificationService.showError('Failed to load rejected loans');
        this.rejectedLoans = [];
      }
    });
  }

  selectRow(loan: RejectedLoan, index: number) {
    this.selectedLoan = loan;
    this.selectedRow = index;
  }

  searchByCustomerName() {
    if (this.searchCustomerName && this.searchCustomerName.trim().length >= 2) {
      this.isSearching = true;
      this.rejectedLoanService.searchByCustomerName(this.searchCustomerName.trim()).subscribe({
        next: (data: RejectedLoan[]) => {
          this.rejectedLoans = data || [];
          this.collectionSize = this.rejectedLoans.length;
          this.isSearching = false;
        },
        error: (error) => {
          console.error('Error searching by customer name:', error);
          this.notificationService.showError('Search failed');
          this.isSearching = false;
        }
      });
    } else {
      this.notificationService.showError('Please enter at least 2 characters');
    }
  }

  searchByCustomerNic() {
    if (this.searchCustomerNic && this.searchCustomerNic.trim().length >= 5) {
      this.isSearching = true;
      this.rejectedLoanService.searchByCustomerNic(this.searchCustomerNic.trim()).subscribe({
        next: (data: RejectedLoan[]) => {
          this.rejectedLoans = data || [];
          this.collectionSize = this.rejectedLoans.length;
          this.isSearching = false;
        },
        error: (error) => {
          console.error('Error searching by customer NIC:', error);
          this.notificationService.showError('Search failed');
          this.isSearching = false;
        }
      });
    } else {
      this.notificationService.showError('Please enter at least 5 characters');
    }
  }

  searchByTelephone() {
    if (this.searchTelephone && this.searchTelephone.trim().length >= 3) {
      this.isSearching = true;
      this.rejectedLoanService.searchByTelephone(this.searchTelephone.trim()).subscribe({
        next: (data: RejectedLoan[]) => {
          this.rejectedLoans = data || [];
          this.collectionSize = this.rejectedLoans.length;
          this.isSearching = false;
        },
        error: (error) => {
          console.error('Error searching by telephone:', error);
          this.notificationService.showError('Search failed');
          this.isSearching = false;
        }
      });
    } else {
      this.notificationService.showError('Please enter at least 3 characters');
    }
  }

  clearSearch() {
    this.searchCustomerName = '';
    this.searchCustomerNic = '';
    this.searchTelephone = '';
    this.selectedLoan = null;
    this.selectedRow = null;
    this.page = 1;
    this.loadRejectedLoans();
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.loadRejectedLoans();
  }
}
