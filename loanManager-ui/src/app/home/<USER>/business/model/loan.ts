import {<PERSON>anP<PERSON>} from "./loanPlan";
import {<PERSON><PERSON><PERSON>} from "../../borrower/model/borrower";
import {MetaData} from "../../../core/model/metaData";

export class Loan {
  public id: string;
  public loanNo: string;
  public dateTime: Date;
  public loanPlan: LoanPlan;
  public borrower: Borrower;
  public loanAmount: number;
  public overPaidAmount: number;
  public loanAmountWithInterest: number;
  public installmentAmount: number;
  public installmentLeft: number;
  public balance: number;
  public arrearsAmount: number;
  public paidAmount: number;
  public status: MetaData;
  public statusCode: string;
  public nextPaymentRecordDate: Date;
  public settlementDate: Date;
  public approvedDate: Date;
  public approvedBy: string;
  public active: boolean;
  public createdDate: Date;
  public rejectedDate: Date;
  public rejectedReason: string;
  public createdBy: string;
  public lastModifiedDate: Date;
  public lastModifiedBy: string;
}
